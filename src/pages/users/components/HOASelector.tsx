import { useState, useEffect, useMemo } from 'react';
import { Button } from '@/components/ui/Button';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/Popover';
import { Check, ChevronDown, Loader2 } from 'lucide-react';
import { Association, useAssociationsQuery } from '@/generated/graphql';
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
} from '@/components/ui/Command';
import { ScrollArea } from '@/components/ui/ScrollArea';
import { useIntersectionObserver } from '@/hooks/useIntersectionObserver';
import { useDebounce } from '@/hooks/useDebounce';
import { useApolloClient } from '@/providers/ApolloClient';
import { cn } from '@/lib/utils';

interface HOASelectorProps {
  selectedHOA: Association | null;
  setSelectedHOA: (value: Association | null) => void;
  tempHOASearch: string;
  setTempHOASearch: (value: string) => void;
}

const ITEMS_PER_PAGE = 10;

const HOASelector = ({
  selectedHOA,
  setSelectedHOA,
  tempHOASearch,
  setTempHOASearch,
}: HOASelectorProps) => {
  const [isOpen, setIsOpen] = useState(false);
  const [page, setPage] = useState(1);
  const [isFetchingMore, setIsFetchingMore] = useState(false);
  const search = useDebounce(tempHOASearch, 500);
  const [searchPlaceholder, setSearchPlaceholder] = useState('Search HOA');

  const { client } = useApolloClient() || {};

  const {
    data: associationsData,
    fetchMore,
    loading,
  } = useAssociationsQuery({
    variables: {
      paginationArgs: {
        page,
        limit: ITEMS_PER_PAGE,
      },
      search,
    },
  });

  const associations = useMemo(
    () => associationsData?.associations?.items ?? [],
    [associationsData?.associations?.items]
  );

  const hasLoadMore = associations.length >= ITEMS_PER_PAGE;

  const total = useMemo(() => associationsData?.associations?.total || 0, [associationsData]);
  const hasMore = useMemo(
    () => associations.length > 0 && total / ITEMS_PER_PAGE > page,
    [associations, total, page]
  );

  const loadMore = async () => {
    if (hasMore && !loading && !isFetchingMore) {
      setIsFetchingMore(true);
      try {
        await fetchMore({
          variables: {
            paginationArgs: {
              page: page + 1,
              limit: ITEMS_PER_PAGE,
            },
            ...(search ? { search } : {}),
          },
        });
        setPage(page + 1);
      } finally {
        setIsFetchingMore(false);
      }
    }
  };

  const loadMoreRef = useIntersectionObserver({
    hasNextPage: hasMore,
    fetchNextPage: loadMore,
    isFetchingNextPage: isFetchingMore,
    threshold: 0.4,
  });

  const filteredAssociations = associations;

  const displayedAssociation = selectedHOA;

  useEffect(() => {
    setPage(1);
  }, [search]);

  return (
    <Popover modal open={isOpen} onOpenChange={setIsOpen}>
      <PopoverTrigger asChild>
        <Button
          variant='outline'
          role='combobox'
          aria-expanded={isOpen}
          className={cn('w-full justify-between text-[#191E3B] font-normal', {
            'text-muted-foreground': !selectedHOA,
          })}
          disabled={loading && associations.length === 0}
        >
          {loading && associations.length === 0 ? (
            <Loader2 className='h-4 w-4 animate-spin' />
          ) : displayedAssociation ? (
            displayedAssociation.name
          ) : (
            'HOA Association'
          )}
          <ChevronDown className='ml-2 h-4 w-4 shrink-0 opacity-50' />
        </Button>
      </PopoverTrigger>
      <PopoverContent
        className='w-[--radix-popover-trigger-width] p-0'
        onOpenAutoFocus={(event) => event.preventDefault()}
      >
        <Command shouldFilter={false}>
          <CommandInput
            placeholder={searchPlaceholder}
            onBlur={() => setSearchPlaceholder('Search HOA ...')}
            onFocus={() => setSearchPlaceholder('')}
            value={tempHOASearch}
            onValueChange={setTempHOASearch}
            className='h-9'
          />
          <CommandEmpty>
            {loading ? 'Loading...' : associations.length === 0 ? 'No Results' : ''}
          </CommandEmpty>
          <CommandGroup>
            <ScrollArea className={cn('', hasLoadMore ? 'h-64' : 'h-fit')}>
              {filteredAssociations.map((association) => (
                <CommandItem
                  key={association.id}
                  value={association.name}
                  onSelect={() => {
                    setSelectedHOA({
                      id: association.id,
                      name: association.name,
                    });
                    setIsOpen(false);
                  }}
                  className='flex items-center justify-between'
                >
                  <span>{association.name}</span>
                  {association.id === selectedHOA?.id && <Check className='h-4 w-4 text-primary' />}
                </CommandItem>
              ))}
              {hasLoadMore && (
                <div ref={loadMoreRef} className='w-full h-8 flex items-center justify-center'>
                  {isFetchingMore && <Loader2 className='h-4 w-4 animate-spin text-primary' />}
                </div>
              )}
            </ScrollArea>
          </CommandGroup>
        </Command>
      </PopoverContent>
    </Popover>
  );
};

export default HOASelector;

import { useCallback } from 'react';
import { useToggleUserClubFeatureMutation } from '@/generated/graphql';
import { toast } from '@/hooks/useToast';

export const useMemberActions = () => {
  const [toggleUserClubFeatureMutation] = useToggleUserClubFeatureMutation({
    refetchQueries: ['AdminClubMembers'],
    onCompleted: () => {
      toast({
        description: 'User club access has been disabled.',
        variant: 'success',
      });
    },
    onError: (error) => {
      toast({
        description: error.message || 'Failed to disable user club access.',
        variant: 'destructive',
      });
    },
  });

  const handleDisableClubAccessMember = useCallback(
    async (userId: string) => {
      if (!userId) {
        toast({
          title: 'Error',
          description: 'Missing required information to disable user club access.',
          variant: 'destructive',
        });
        return;
      }

      try {
        await toggleUserClubFeatureMutation({
          variables: {
            userId: userId,
          },
        });
      } catch (error) {
        // Error is already handled in onError callback
        console.error('Failed to remove member:', error);
      }
    },
    [toggleUserClubFeatureMutation]
  );

  return {
    handleDisableClubAccessMember,
  };
};

import { AdminClubMembership } from '@/generated/graphql';
import RemoveUserModal from '@/components/modals/remove-modal/RemoveModal';

interface MemberActions {
  selectedMember: AdminClubMembership | null;
  isRemoveModalOpen: boolean;
  isDisablingClubAccess: boolean;
  handleDisableClubAccessMemberModal: (member: AdminClubMembership) => void;
  handleCloseDisableClubAccessMemberModal: () => void;
  handleDisableClubAccessMember: () => Promise<void>;
}

interface MemberModalsProps {
  memberActions: MemberActions;
}

export const MemberModals = ({ memberActions }: MemberModalsProps) => {
  return (
    <>
      {/* Remove Member Modal */}
      <RemoveUserModal
        isOpen={memberActions.isRemoveModalOpen}
        onOpenChange={memberActions.handleCloseDisableClubAccessMemberModal}
        onCancel={memberActions.handleCloseDisableClubAccessMemberModal}
        onConfirm={memberActions.handleDisableClubAccessMember}
        isLoading={memberActions.isDisablingClubAccess}
        title='Disable Club Access'
        confirmText='Disable'
        description={`Are you sure you want to disable this user's club access? Once disabled, they will no longer be able to join or post in this club.`}
      />
    </>
  );
};

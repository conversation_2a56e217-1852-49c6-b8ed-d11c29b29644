import { Checkbox } from '@/components/ui/Checkbox';
import { HeaderColumn } from '@/components/ui/table/HeaderColumn';
import SkeletonCell from '@/components/ui/table/SkeletonCell';
import { ColumnDef } from '@tanstack/react-table';
import { AdminClubMembership } from '@/generated/graphql';
import { format } from 'date-fns';
import ActionCell from '@/components/ui/table/ActionCell';

interface MemberColumnsProps {
  isLoading: boolean;
  showActions: boolean;
  selectedRowId: string | null;
  actionCellRef: React.RefObject<HTMLDivElement>;
  setSelectedRowId: (id: string | null) => void;
  onDisableClubAccess?: (userId: string) => void;
}

export function generateMemberColumns({
  isLoading,
  showActions,
  selectedRowId,
  actionCellRef,
  setSelectedRowId,
  onDisableClubAccess,
}: MemberColumnsProps) {
  const columns: ColumnDef<AdminClubMembership>[] = [
    {
      accessorKey: 'select',
      header: ({ table }) => (
        <Checkbox
          checked={table.getIsSomeRowsSelected() || table.getIsAllRowsSelected()}
          onCheckedChange={() => table.toggleAllRowsSelected()}
          className='cursor-pointer w-5 h-5'
        />
      ),
      cell: ({ row }) => (
        <Checkbox
          className='w-5 h-5'
          checked={row.getIsSelected()}
          onClick={(e) => {
            e.stopPropagation();
          }}
          onCheckedChange={() => row.toggleSelected()}
        />
      ),
      minSize: 50,
      maxSize: 50,
    },
    {
      accessorKey: 'id',
      id: 'ID',
      header: ({ column }) => <HeaderColumn column={column}>User ID</HeaderColumn>,
      cell: ({ row }) => {
        const id = row.original.clubProfile?.id || 'N/A';
        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex flex-col w-full'>
              <span className='truncate text-gray-900 font-medium'>{id}</span>
            </div>
          </SkeletonCell>
        );
      },
      minSize: 100,
      maxSize: 150,
      meta: {
        padding: '20px 16px',
      },
    },
    {
      id: 'displayName',
      accessorKey: 'displayName',
      header: ({ column }) => <HeaderColumn column={column}>Name</HeaderColumn>,
      cell: ({ row }) => {
        const displayName = row.original.clubProfile?.user?.firstName || '';

        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <div className='flex items-center gap-3'>
              <span className='font-medium text-gray-900'>{displayName}</span>
            </div>
          </SkeletonCell>
        );
      },
      minSize: 120,
      size: 120,
      enableSorting: false,
    },
    {
      accessorKey: 'lastName',
      header: ({ column }) => <HeaderColumn column={column}>Last Name</HeaderColumn>,
      cell: ({ row }) => {
        const lastName = row.original.clubProfile?.user?.lastName || '';

        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <span className='truncate text-gray-900 font-medium'>{lastName}</span>
          </SkeletonCell>
        );
      },
      minSize: 120,
      size: 150,
      enableSorting: false,
    },
    {
      accessorKey: 'email',
      header: ({ column }) => <HeaderColumn column={column}>Email</HeaderColumn>,
      cell: ({ row }) => {
        const email = row.original.clubProfile?.user?.email || '';

        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <span className='truncate text-foreground font-normal'>{email}</span>
          </SkeletonCell>
        );
      },
      minSize: 200,
      size: 250,
      enableSorting: false,
    },
    {
      accessorKey: 'dateJoined',
      id: 'JOINED_DATE',
      header: ({ column }) => <HeaderColumn column={column}>Date Joined</HeaderColumn>,
      cell: ({ row }) => {
        const joinedAt = row.original.joinedAt || '';

        return (
          <SkeletonCell isLoading={isLoading} skeletonCount={1}>
            <span className='whitespace-nowrap text-foreground font-normal'>
              {joinedAt ? format(new Date(joinedAt), 'MM/dd/yyyy') : ''}
            </span>
          </SkeletonCell>
        );
      },
      minSize: 120,
      size: 150,
    },
    ...(showActions
      ? [
          {
            accessorKey: 'actions',
            header: '',
            cell: ({ row }) => {
              const isSelected = selectedRowId === row.original.id;
              const userId = row.original.clubProfile?.user?.id;
              const isDisabled = !row.original.clubProfile?.user?.canUseClubs;

              return (
                <SkeletonCell isLoading={isLoading} skeletonCount={1}>
                  <ActionCell
                    ref={actionCellRef}
                    isSelected={isSelected}
                    setSelectedRowId={setSelectedRowId}
                    actions={[
                      {
                        label: 'Disable Club Access',
                        onClick: () => {
                          onDisableClubAccess?.(userId ?? '');
                        },
                        disabled: isDisabled,
                      },
                    ]}
                  />
                </SkeletonCell>
              );
            },
            minSize: 80,
            maxSize: 80,
          } as ColumnDef<AdminClubMembership>,
        ]
      : []),
  ];
  return columns;
}

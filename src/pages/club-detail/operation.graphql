query AdminClubDetailById($adminClubByIdId: ID!) {
  adminClubById(id: $adminClubByIdId) {
    id
    activatedAt
    memberCount
    clubTemplate {
      id
      name
      description
      about
      img {
        id
        filename
        key
        mimeType
        size
        status
        createdAt
        updatedAt
        url
      }
      category
      newPost
      hasJoined
      memberCount
    }
  }
}

query ClubMembers(
  $clubTemplateId: ID!
  $paginationArgs: PaginationArgs
  $filter: ClubMembersFilterInput
  $orderBy: ClubMembersOrderInput
) {
  clubMembers(
    clubTemplateId: $clubTemplateId
    paginationArgs: $paginationArgs
    filter: $filter
    orderBy: $orderBy
  ) {
    items {
      id
      clubProfile {
        id
        displayName
        img {
          id
          filename
          key
          mimeType
          size
          status
          createdAt
          updatedAt
          url
        }
      }
      status
      joinedAt
      deletedAt
    }
    total
    page
    limit
  }
}

query AdminClubPosts(
  $clubId: ID!
  $paginationArgs: PaginationArgs
  $filter: AdminClubPostsFilterInput
) {
  adminClubPosts(clubId: $clubId, paginationArgs: $paginationArgs, filter: $filter) {
    items {
      id
      clubId
      clubProfile {
        id
        displayName
        img {
          id
          filename
          key
          mimeType
          size
          status
          createdAt
          updatedAt
          url
        }
        user {
          id
          firstName
          lastName
          email
          role
          canUseClubs
        }
        createdAt
        updatedAt
      }
      content
      isPinned
      reactions {
        id
        postId
        clubProfileId
        createdAt
      }
      reports {
        id
        clubPost {
          id
          clubId
          clubProfile {
            id
            displayName
          }
          content
          isPinned
          reactionCount
          createdAt
          updatedAt
        }
        category {
          id
          code
          title
          description
          ordering
          createdAt
          updatedAt
        }
        reporter {
          id
          displayName
          createdAt
          updatedAt
        }
        status
        details
        createdAt
      }
      reactionCount
      createdAt
      updatedAt
      deletedAt
    }
    total
    page
    limit
  }
}

query AdminClubPostById($postId: ID!) {
  adminClubPostById(postId: $postId) {
    id
    clubId
    clubProfile {
      id
      displayName
      img {
        id
        filename
        key
        mimeType
        size
        status
        createdAt
        updatedAt
        url
      }
      user {
        id
        firstName
        lastName
        email
        addresses {
          id
          street
          city
          state
          zipCode
          isActive
          isPrimaryUnit
          source
          createdAt
          updatedAt
          isSameAsUnitAddress
        }
        phone
        dob
        birthdayMonth
        birthdayDay
        dateJoined
        association {
          id
          name
        }
        contact {
          id
          salesforceId
        }
        role
        isDeleted
      }
      createdAt
      updatedAt
    }
    content
    isPinned
    reactions {
      id
      postId
      clubProfileId
      createdAt
    }
    reports {
      id
      clubPost {
        id
        clubId
        clubProfile {
          id
          displayName
        }
        content
        isPinned
        reactionCount
        createdAt
        updatedAt
      }
      category {
        id
        code
        title
        description
        ordering
        createdAt
        updatedAt
      }
      reporter {
        id
        displayName
        createdAt
        updatedAt
      }
      status
      details
      createdAt
    }
    reactionCount
    createdAt
    updatedAt
    deletedAt
  }
}

query AdminClubEvents($clubId: ID!, $paginationArgs: PaginationArgs, $filter: AdminClubEventsFilterInput) {
  adminClubEvents(clubId: $clubId, paginationArgs: $paginationArgs, filter: $filter) {
    items {
      id
      name
      startTime
      endTime
      location
      description
      reactionCount
      clubProfile {
        id
        displayName
        img {
          url
        }
        user {
          id
          firstName
          lastName
          email
          phone
          dob
          birthdayMonth
          birthdayDay
          dateJoined
          role
          isDeleted
          canUseClubs
        }
        createdAt
        updatedAt
      }
      reports {
        id
        category {
          id
          code
          title
          description
          ordering
          createdAt
          updatedAt
        }
        status
        details
        createdAt
      }
      createdAt
      updatedAt
    }
    total
    page
    limit
  }
}

query UsersCreateEvents(
  $clubId: ID!
  $paginationArgs: PaginationArgs
  $filter: UsersCreateEventsFilterInput
) {
  usersCreateEvents(clubId: $clubId, paginationArgs: $paginationArgs, filter: $filter) {
    items {
      id
      firstName
      lastName
      email
      phone
      dob
      birthdayMonth
      birthdayDay
      dateJoined
      role
      isDeleted
      canUseClubs
    }
    total
    page
    limit
  }
}

query AdminClubEventById($clubEventId: ID!) {
  adminClubEventById(clubEventId: $clubEventId) {
    id
    name
    startTime
    endTime
    location
    description
    reactionCount
    clubProfile {
      id
      displayName
      img {
        id
        filename
        key
        mimeType
        size
        status
        createdAt
        updatedAt
        url
      }
    }
    createdAt
    updatedAt
  }
}

query ClubRequestById($clubRequestByIdId: ID!) {
  clubRequestById(id: $clubRequestByIdId) {
    id
    clubProfile {
      id
      displayName
      img {
        id
        filename
        key
        mimeType
        size
        status
        createdAt
        updatedAt
        url
      }
    }
    clubName
    clubDescription
    category
    clubAbout
    status
    createdAt
  }
}

mutation AdminRemoveClubPostById($postId: ID!) {
  adminRemoveClubPostById(postId: $postId) {
    id
    clubId
    content
    isPinned
    reactionCount
    hasReacted
    createdAt
    updatedAt
  }
}

query AdminClubMembers(
  $clubId: ID!
  $paginationArgs: PaginationArgs
  $filter: ClubMembersFilterInput
  $orderBy: ClubMembersOrderInput
) {
  adminClubMembers(
    clubId: $clubId
    paginationArgs: $paginationArgs
    filter: $filter
    orderBy: $orderBy
  ) {
    items {
      id
      clubProfile {
        id
        displayName
        img {
          id
          filename
          key
          mimeType
          size
          status
          createdAt
          updatedAt
          url
        }
        user {
          id
          firstName
          lastName
          email
          phone
          dob
          birthdayMonth
          birthdayDay
          dateJoined
          role
          isDeleted
          canUseClubs
        }
        createdAt
        updatedAt
      }
      status
      joinedAt
      deletedAt
    }
    total
    page
    limit
  }
}

query UsersCreatePosts(
  $clubId: ID!
  $paginationArgs: PaginationArgs
  $filter: UsersCreatePostFilterInput
) {
  usersCreatePosts(clubId: $clubId, paginationArgs: $paginationArgs, filter: $filter) {
    items {
      id
      firstName
      lastName
      email
      phone
      dob
      birthdayMonth
      birthdayDay
      dateJoined
      role
      isDeleted
      canUseClubs
    }
    limit
    page
    total
  }
}

mutation AdminUnflagReportsByPostId($postId: ID!) {
  adminUnflagReportsByPostId(postId: $postId) {
    clubId
    id
    updatedAt
    createdAt
  }
}

mutation AdminDeleteClubEvent($clubEventId: ID!) {
  adminDeleteClubEvent(clubEventId: $clubEventId) {
    id
    name
    createdAt
    updatedAt
  }
}

mutation AdminUnflagReportsByEventId($eventId: ID!) {
  adminUnflagReportsByEventId(eventId: $eventId) {
    id
    name
    createdAt
    updatedAt
  }
}

mutation RemoveClubMember($input: RemoveClubMemberInput!) {
  removeClubMember(input: $input) {
    clubTemplateId
    membershipId
  }
}

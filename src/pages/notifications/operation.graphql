query AdminNotifications($paginationArgs: PaginationArgs) {
  adminNotifications(paginationArgs: $paginationArgs) {
    items {
      id
      type
      isRead
      createdAt
      updatedAt
      reportedAt
      payload {
        postId
        eventId
      }
      clubPost {
        id
        clubId
        clubProfile {
          id
          displayName
          img {
            id
            filename
            key
            mimeType
            size
            status
            createdAt
            updatedAt
            url
          }
          user {
            id
            firstName
            lastName
            email
            addresses {
              id
              street
              city
              state
              zipCode
              isActive
              isPrimaryUnit
              source
              createdAt
              updatedAt
              isSameAsUnitAddress
            }
            phone
            dob
            birthdayMonth
            birthdayDay
            dateJoined
            association {
              id
              name
              canUseClubs
            }
            contact {
              id
              salesforceId
            }
            role
            isDeleted
            canUseClubs
          }
          createdAt
          updatedAt
        }
        content
        isPinned
        reactions {
          id
          postId
          clubProfileId
          createdAt
        }
        reports {
          id
          clubPost {
            id
            clubId
            clubProfile {
              id
              displayName
            }
            content
            isPinned
            isNew
            reactionCount
            hasReacted
            createdAt
            updatedAt
          }
          clubEvent {
            id
            name
            startTime
            endTime
            location
            description
            reactionCount
            isNew
            hasReacted
            createdAt
            updatedAt
          }
          category {
            id
            code
            title
            description
            ordering
            createdAt
            updatedAt
          }
          reporter {
            id
            displayName
            createdAt
            updatedAt
          }
          status
          details
          createdAt
        }
        reactionCount
        createdAt
        updatedAt
        deletedAt
      }
      clubEvent {
        clubId
        id
        name
        startTime
        endTime
        location
        description
        reactionCount
        createdAt
        updatedAt
        clubProfile {
          id
          displayName
          user {
            association {
              id
              name
              canUseClubs
            }
          }
          createdAt
          updatedAt
        }
        reports {
          id
          category {
            id
            code
            title
            description
          }
          status
          details
          createdAt
        }
      }
      action
    }
    limit
    page
    total
  }
}

mutation MarkAdminNotificationsAsRead($input: MarkNotificationsAsReadInput!) {
  markAdminNotificationsAsRead(input: $input)
}
import { SortingState } from '@tanstack/react-table';
import { useEffect, useRef, useState } from 'react';
import { useSearchParams } from 'react-router-dom';

interface InitialSort {
  id: string;
  desc?: boolean;
}

interface UseSortOptions {
  initialSort?: InitialSort;
}

export default function useSort(options?: UseSortOptions) {
  const [searchParams, setSearchParams] = useSearchParams();
  const [sorting, setSorting] = useState<SortingState>([]);
  const isInitializedRef = useRef(false);

  // Initialize sorting from URL on mount, fallback to initial sort option
  useEffect(() => {
    if (isInitializedRef.current) return;

    const sortField = searchParams.get('sortField');
    const sortDir = searchParams.get('sortDir');

    if (sortField) {
      // Priority 1: Use URL params if available
      setSorting([
        {
          id: sortField,
          desc: sortDir === 'desc',
        },
      ]);
    } else if (options?.initialSort) {
      // Priority 2: Use initial sort option if provided
      setSorting([
        {
          id: options.initialSort.id,
          desc: options.initialSort.desc ?? false,
        },
      ]);
    }

    isInitializedRef.current = true;
  }, [searchParams, options?.initialSort]);

  // Update URL when sorting changes (but not during initialization)
  useEffect(() => {
    if (!isInitializedRef.current) return;

    const newParams = new URLSearchParams(searchParams);

    if (sorting.length > 0) {
      newParams.set('sortField', sorting[0].id);
      newParams.set('sortDir', sorting[0].desc ? 'desc' : 'asc');
    } else {
      newParams.delete('sortField');
      newParams.delete('sortDir');
    }

    // Only update if the params actually changed
    const currentParams = searchParams.toString();
    const newParamsString = newParams.toString();

    if (currentParams !== newParamsString) {
      setSearchParams(newParams, { replace: true });
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [sorting, searchParams]);

  return { sorting, setSorting };
}
